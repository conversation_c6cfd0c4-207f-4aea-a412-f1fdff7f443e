#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes.h"

int main()
{
    // 您提供的密钥
    uint8_t key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };
    
    // 您提供的16字节种子（明文）
    uint8_t plaintext[16] = {
        0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
    };
    
    // 创建一个副本用于加密（因为加密会修改原数据）
    uint8_t ciphertext[16];
    memcpy(ciphertext, plaintext, 16);
    
    // 初始化AES上下文
    struct AES_ctx ctx;
    AES_init_ctx(&ctx, key);
    
    printf("=== AES ===\n\n");
    
    // 打印密钥
    printf(" (Key): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", key[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 打印明文
    printf(" (Plaintext): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", plaintext[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 执行AES ECB加密
    AES_ECB_encrypt(&ctx, ciphertext);
    
    // 打印密文
    printf(" (Ciphertext): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", ciphertext[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 将密文保存到文件中，供解密程序使用
    FILE *fp = fopen("ciphertext.bin", "wb");
    if(fp != NULL) {
        fwrite(ciphertext, 1, 16, fp);
        fclose(fp);
        printf(" ciphertext.bin 文件中\n");
    } else {
        printf("\n");
    }
    
    return 0;
}
