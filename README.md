# AES 加密解密演示

这个项目演示了如何使用AES算法对16字节数据进行加密和解密。

## 文件说明

- `aes.h` / `aes.c` - AES算法库文件
- `aes_encrypt.c` - AES加密程序源码
- `aes_decrypt.c` - AES解密程序源码
- `Makefile` - 编译配置文件（需要make工具）
- `run_demo.bat` - Windows批处理文件，一键运行演示

## 使用的参数

### 密钥 (16字节)
```
0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
```

### 明文种子 (16字节)
```
0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F, 0x10
```

### 加密结果 (密文)
```
0x43, 0x7A, 0x76, 0x2C, 0x39, 0x17, 0xB1, 0x28,
0x83, 0x8A, 0x39, 0x52, 0x66, 0xFB, 0x0D, 0x19
```

## 编译和运行

### 方法1：使用批处理文件（推荐）
```bash
run_demo.bat
```

### 方法2：手动编译
```bash
# 编译加密程序
gcc -Wall -Wextra -std=c99 -O2 -o aes_encrypt.exe aes_encrypt.c aes.c

# 编译解密程序
gcc -Wall -Wextra -std=c99 -O2 -o aes_decrypt.exe aes_decrypt.c aes.c

# 运行加密
aes_encrypt.exe

# 运行解密
aes_decrypt.exe
```

### 方法3：使用Makefile（需要make工具）
```bash
make all
make demo
```

## 程序功能

1. **aes_encrypt.exe**：
   - 使用指定的密钥对16字节种子进行AES-128 ECB加密
   - 显示密钥、明文和生成的密文
   - 将密文保存到 `ciphertext.bin` 文件

2. **aes_decrypt.exe**：
   - 从 `ciphertext.bin` 文件读取密文
   - 使用相同的密钥进行AES-128 ECB解密
   - 显示解密过程和结果
   - 验证解密结果是否与原始明文一致

## 注意事项

- 使用的是AES-128 ECB模式
- ECB模式在实际应用中不够安全，仅用于演示
- 程序处理的是固定的16字节数据块
- 密文文件 `ciphertext.bin` 用于在加密和解密程序之间传递数据

## 清理文件

```bash
# 删除生成的可执行文件和密文文件
del aes_encrypt.exe aes_decrypt.exe ciphertext.bin
```
