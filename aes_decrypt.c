#include <stdio.h>
#include <stdint.h>
#include <string.h>
#include "aes.h"

int main()
{
    // 相同的密钥
    uint8_t key[16] = {
        0x7D, 0xA2, 0x58, 0x19, 0xE3, 0x35, 0x8F, 0x52,
        0xB4, 0x26, 0x99, 0xC7, 0x74, 0x11, 0xE8, 0x5A
    };
    
    uint8_t ciphertext[16];
    uint8_t decrypted[16];
    
    printf("=== AES ===\n\n");
    
    // 从文件读取密文
    FILE *fp = fopen("ciphertext.bin", "rb");
    if(fp == NULL) {
        printf(" ciphertext.bin\n");
        printf("please \n");
        return 1;
    }
    
    size_t bytes_read = fread(ciphertext, 1, 16, fp);
    fclose(fp);
    
    if(bytes_read != 16) {
        printf("error expected 16 bytes, read %zu byte\n", bytes_read);
        return 1;
    }
    
    // 复制密文到解密缓冲区
    memcpy(decrypted, ciphertext, 16);
    
    // 初始化AES上下文
    struct AES_ctx ctx;
    AES_init_ctx(&ctx, key);
    
    // 打印密钥
    printf(" (Key): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", key[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 打印密文
    printf("ciphertext (Ciphertext): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", ciphertext[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 执行AES ECB解密
    AES_ECB_decrypt(&ctx, decrypted);
    
    // 打印解密后的明文
    printf(" (Decrypted): ");
    for(int i = 0; i < 16; i++) {
        printf("0x%02X", decrypted[i]);
        if(i < 15) printf(", ");
    }
    printf("\n\n");
    
    // 验证解密结果
    uint8_t original[16] = {
        0x1B, 0x36, 0xB4, 0xD3, 0x72, 0xB0, 0x33, 0xEA,
        0x7E, 0x2F, 0xD7, 0x96, 0x25, 0xCC, 0xD0, 0x0C
    };
    
    if(memcmp(decrypted, original, 16) == 0) {
        printf("OK\n");
    } else {
        printf("ERROR\n");
    }
    
    return 0;
}
