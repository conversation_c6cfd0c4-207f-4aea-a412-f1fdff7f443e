CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -O2

# 目标文件
TARGETS = aes_encrypt aes_decrypt

# 源文件
AES_SRC = aes.c
ENCRYPT_SRC = aes_encrypt.c
DECRYPT_SRC = aes_decrypt.c

# 默认目标
all: $(TARGETS)

# 编译加密程序
aes_encrypt: $(ENCRYPT_SRC) $(AES_SRC)
	$(CC) $(CFLAGS) -o $@ $^

# 编译解密程序
aes_decrypt: $(DECRYPT_SRC) $(AES_SRC)
	$(CC) $(CFLAGS) -o $@ $^

# 运行演示
demo: all
	@echo "=== 运行AES加密演示 ==="
	./aes_encrypt
	@echo ""
	@echo "=== 运行AES解密演示 ==="
	./aes_decrypt

# 清理生成的文件
clean:
	rm -f $(TARGETS) ciphertext.bin

# 清理所有生成的文件（包括可执行文件）
distclean: clean
	rm -f *.exe

.PHONY: all demo clean distclean
